"use client";

import Image from "next/image";
import { useEffect, useRef, useState, useCallback, useMemo } from "react";

// Types
interface ScrollAnimationHook {
  scrollProgress: number;
  isVisible: boolean;
}

interface CredibilityItem {
  id: string;
  text: string;
}

interface BulletPoint {
  text: string;
  system: string;
}

interface Testimonial {
  id: string;
  quote: string;
  name: string;
  title: string;
}

// Constants
const CREDIBILITY_ITEMS: CredibilityItem[] = [
  {
    id: "architects",
    text: "Built by systems architects, not service providers",
  },
  { id: "powered", text: "Powered by AI, automation, and clean workflows" },
  {
    id: "trusted",
    text: "Trusted by consultants, clinics, and scaling businesses",
  },
  { id: "installed", text: "Installed in <14 days. Engineered for scale." },
];

const BULLET_POINTS: BulletPoint[] = [
  { text: "Generate qualified leads", system: "(Kairo)" },
  { text: "Close more sales", system: "(Airo)" },
  { text: "Deliver premium onboarding", system: "(Onboard)" },
  { text: "Increase lifetime value", system: "(Nurture, Retain)" },
];

const TESTIMONIALS: Testimonial[] = [
  {
    id: "sarah",
    quote:
      "Since we installed Airo, our close rate went up 33% — without hiring anyone new.",
    name: "Sarah T.",
    title: "Consultant, Dubai",
  },
  {
    id: "david",
    quote:
      "Kairo gave us 40 leads in 3 weeks — and 9 booked calls. All automated.",
    name: "David R.",
    title: "Agency Owner",
  },
];

// Optimized scroll animation hook with throttling and intersection observer
function useScrollAnimation(
  ref: React.RefObject<HTMLElement | null>
): ScrollAnimationHook {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const rafId = useRef<number | undefined>(undefined);

  const handleScroll = useCallback(() => {
    if (!ref.current) return;

    if (rafId.current) {
      cancelAnimationFrame(rafId.current);
    }

    rafId.current = requestAnimationFrame(() => {
      if (!ref.current) return;

      const rect = ref.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const sectionHeight = rect.height;

      // Calculate how much of the section is visible
      const visibleTop = Math.max(0, windowHeight - rect.top);
      const visibleBottom = Math.max(0, rect.bottom);
      const visibleHeight = Math.min(visibleTop, visibleBottom, sectionHeight);

      // Calculate scroll progress (0 to 1)
      const progress = Math.min(Math.max(visibleHeight / sectionHeight, 0), 1);
      setScrollProgress(progress);
    });
  }, [ref]);

  useEffect(() => {
    // Intersection Observer for better performance
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
        if (entry.isIntersecting) {
          window.addEventListener("scroll", handleScroll, { passive: true });
          handleScroll(); // Initial call
        } else {
          window.removeEventListener("scroll", handleScroll);
        }
      },
      { rootMargin: "50px 0px" }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
      window.removeEventListener("scroll", handleScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [ref, handleScroll]);

  return { scrollProgress, isVisible };
}

// Memoized animation styles hook
function useAnimationStyles(
  scrollProgress: number,
  translateX: number,
  delay = 0
) {
  return useMemo(
    () => ({
      transform: `translateX(${(1 - scrollProgress) * translateX}px)`,
      opacity: scrollProgress,
      transitionDelay: delay > 0 ? `${delay}ms` : undefined,
    }),
    [scrollProgress, translateX, delay]
  );
}

// Hero Section Component
const HeroSection = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const { scrollProgress } = useScrollAnimation(sectionRef);

  const titleStyles = useAnimationStyles(scrollProgress, 100);
  const mainTitleStyles = useAnimationStyles(scrollProgress, 150, 200);
  const subtitleStyles = useAnimationStyles(scrollProgress, 200, 400);

  return (
    <section
      ref={sectionRef}
      className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white"
      aria-label="Hero section"
    >
      {/* Particle Effects Background */}
      <div className="absolute inset-0 z-0" aria-hidden="true">
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30">
          <Image
            src="/particles.jpg"
            alt=""
            fill
            className="object-cover object-left"
            priority
            sizes="50vw"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-5">
              <h1
                className="text-5xl lg:text-5xl font-extralight text-gray-400 leading-tight transform transition-all duration-700 ease-out"
                style={titleStyles}
              >
                Ascenda
              </h1>
              <h1
                className="text-5xl lg:text-6xl font-medium text-gray-900 leading-tight transform transition-all duration-700 ease-out"
                style={mainTitleStyles}
              >
                Install the Growth Infrastructure
              </h1>
              <p
                className="text-xl lg:text-2xl text-gray-700 leading-relaxed transform transition-all duration-700 ease-out"
                style={subtitleStyles}
              >
                Your Business{" "}
                <span className="font-extralight lg:text-5xl text-gray-400">
                  Has Been Missing
                </span>
              </p>
            </div>
          </div>

          {/* Right Column - Visual Space for Particles */}
          <div className="hidden lg:block relative" aria-hidden="true">
            <div className="w-full h-96 relative">
              {/* This space is intentionally left for the particle effects background */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Plug and Play Systems Section Component
const PlugAndPlaySection = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const { scrollProgress } = useScrollAnimation(sectionRef);

  const headerStyles = useAnimationStyles(scrollProgress, 100);
  const titleStyles = useAnimationStyles(scrollProgress, 150);
  const description1Styles = useAnimationStyles(scrollProgress, 120);
  const description2Styles = useAnimationStyles(scrollProgress, 140);

  return (
    <section
      ref={sectionRef}
      className="min-h-screen bg-gray-100 flex items-center overflow-hidden"
      aria-label="Plug and play systems"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Visual/Image Space */}
          <div
            className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-1"
            aria-hidden="true"
          >
            {/* Placeholder for visual content */}
          </div>

          {/* Right Column - Content */}
          <div className="space-y-6 order-1 lg:order-2">
            <div className="space-y-4">
              <p
                className="text-sm font-medium pt-3 text-gray-500 uppercase tracking-wider transform transition-all duration-700 ease-out"
                style={headerStyles}
              >
                Ascenda Partners builds
              </p>
              <h2
                className="text-4xl lg:text-9xl font-medium text-gray-900 leading-tight transform transition-all duration-700 ease-out"
                style={titleStyles}
              >
                PLUG AND PLAY SYSTEMS
              </h2>
            </div>

            <div className="space-y-4">
              <p
                className="text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out"
                style={description1Styles}
              >
                <span className="font-medium text-gray-900">
                  that help you generate leads, close more deals, and scale with
                  precision
                </span>
              </p>
              <p
                className="text-base text-gray-600 italic transform transition-all duration-700 ease-out"
                style={description2Styles}
              >
                No fluff. No retainers. Just automated growth machines —
                installed in days.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Credibility/Promise Bar Section Component
const CredibilitySection = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const { scrollProgress } = useScrollAnimation(sectionRef);
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const textStyles = useAnimationStyles(scrollProgress, 100);

  const startCarousel = useCallback(() => {
    intervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % CREDIBILITY_ITEMS.length);
    }, 3000);
  }, []);

  const stopCarousel = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  }, []);

  useEffect(() => {
    startCarousel();
    return stopCarousel;
  }, [startCarousel, stopCarousel]);

  const handleIndicatorClick = useCallback(
    (index: number) => {
      setCurrentIndex(index);
      stopCarousel();
      setTimeout(startCarousel, 3000); // Restart after 3 seconds
    },
    [startCarousel, stopCarousel]
  );

  return (
    <section
      ref={sectionRef}
      className="py-12 bg-white border-t border-gray-200 overflow-hidden"
      aria-label="Company credibility"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="relative h-20 flex items-center justify-center">
          {/* Carousel Container */}
          <div
            className="relative w-full max-w-4xl overflow-hidden"
            role="region"
            aria-label="Credibility carousel"
            aria-live="polite"
          >
            <div
              className="flex transition-transform duration-700 ease-in-out"
              style={{
                transform: `translateX(-${currentIndex * 100}%)`,
              }}
            >
              {CREDIBILITY_ITEMS.map((item, index) => (
                <div
                  key={item.id}
                  className="w-full flex-shrink-0 flex items-center justify-center px-4"
                >
                  <p
                    className="text-lg font-medium text-gray-900 text-center transform transition-all duration-500 ease-out"
                    style={textStyles}
                  >
                    {item.text}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Carousel Indicators */}
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {CREDIBILITY_ITEMS.map((_, index) => (
              <button
                key={index}
                onClick={() => handleIndicatorClick(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-gray-900 w-6"
                    : "bg-gray-300 hover:bg-gray-400"
                }`}
                aria-label={`Go to slide ${index + 1}`}
                aria-current={index === currentIndex ? "true" : "false"}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

// What We Do Section Component
const WhatWeDoSection = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const { scrollProgress } = useScrollAnimation(sectionRef);

  const headerStyles = useAnimationStyles(scrollProgress, 100);
  const titleStyles = useAnimationStyles(scrollProgress, 150);
  const description1Styles = useAnimationStyles(scrollProgress, 120);
  const description2Styles = useAnimationStyles(scrollProgress, 140);
  const noteStyles = useAnimationStyles(scrollProgress, 80);
  const buttonStyles = useAnimationStyles(scrollProgress, 60);

  return (
    <section
      ref={sectionRef}
      className="min-h-screen bg-gray-100 flex items-center overflow-hidden"
      aria-label="What we do"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Content */}
          <div className="space-y-6 order-1 lg:order-1">
            <div className="space-y-6">
              <p
                className="text-sm pt-3 font-medium text-gray-500 uppercase tracking-wider transform transition-all duration-700 ease-out"
                style={headerStyles}
              >
                We Don't Sell Time
              </p>
              <h2
                className="text-4xl lg:text-7xl font-medium text-gray-900 leading-[1.4] transform transition-all duration-700 ease-out"
                style={titleStyles}
              >
                WE INSTALL SYSTEMS THAT MULTIPLY IT.
              </h2>
            </div>

            <div className="space-y-4">
              <p
                className="text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out"
                style={description1Styles}
              >
                At Ascenda, we don't do retainers, vague strategies, or service
                packages.
              </p>
              <p
                className="text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out"
                style={description2Styles}
              >
                We install modular business systems that are fully automated,
                AI-powered, and designed to help you:
              </p>
            </div>

            <ul className="space-y-3 pl-4" role="list">
              {BULLET_POINTS.map((point, index) => {
                const itemStyles = useAnimationStyles(scrollProgress, 100);
                return (
                  <li
                    key={`${point.text}-${index}`}
                    className="flex items-start gap-3 transform transition-all duration-700 ease-out"
                    style={itemStyles}
                  >
                    <span className="text-black font-bold" aria-hidden="true">
                      •
                    </span>
                    <span className="text-base text-gray-700">
                      {point.text}{" "}
                      <span className="font-medium">{point.system}</span>
                    </span>
                  </li>
                );
              })}
            </ul>

            <div className="space-y-4">
              <p
                className="text-base text-gray-600 italic transform transition-all duration-700 ease-out"
                style={noteStyles}
              >
                Each system runs in the background — like a revenue engine that
                never stops.
              </p>

              <div className="pt-4">
                <button
                  type="button"
                  className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 transform focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2"
                  style={buttonStyles}
                >
                  Book a call and we'll show you what system fits you best
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Space */}
          <div
            className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-2"
            aria-hidden="true"
          >
            {/* Placeholder for system diagram or visual */}
          </div>
        </div>
      </div>
    </section>
  );
};

// System Card Component for better reusability
const SystemCard = ({
  title,
  subtitle,
  description,
  features,
  buttonText,
  alignment = "left",
  animationDelay = 0,
  scrollProgress,
}: {
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  buttonText: string;
  alignment?: "left" | "right";
  animationDelay?: number;
  scrollProgress: number;
}) => {
  const isRight = alignment === "right";
  const translateX = isRight ? 150 : -150;
  const cardStyles = useAnimationStyles(scrollProgress, translateX);

  return (
    <div
      className={`bg-gray-50 p-8 lg:p-10 space-y-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 ${
        isRight ? "text-right" : ""
      }`}
      style={{
        ...cardStyles,
        animation: `parallaxFloat${
          isRight ? "2" : "1"
        } 8s ease-in-out infinite ${animationDelay}s`,
      }}
    >
      <div className="space-y-4">
        <div
          className={`flex items-center gap-3 ${isRight ? "justify-end" : ""}`}
        >
          <h3 className="text-2xl lg:text-7xl font-medium text-gray-900">
            {title}
          </h3>
        </div>
        <h4 className="text-xl font-medium text-gray-900">{subtitle}</h4>
        <p className="text-base text-gray-600">{description}</p>
      </div>

      <ul className="space-y-3" role="list">
        {features.map((feature, index) => (
          <li
            key={`${feature}-${index}`}
            className={`flex items-start gap-3 ${isRight ? "justify-end" : ""}`}
          >
            {!isRight && (
              <span className="text-gray-900 font-bold" aria-hidden="true">
                •
              </span>
            )}
            <span className="text-sm text-gray-700">{feature}</span>
            {isRight && (
              <span className="text-gray-900 font-bold" aria-hidden="true">
                •
              </span>
            )}
          </li>
        ))}
      </ul>

      <div className={`pt-4 ${isRight ? "flex justify-end" : ""}`}>
        <button
          type="button"
          className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2"
        >
          {buttonText}
        </button>
      </div>
    </div>
  );
};

// Featured System Section Component
const FeaturedSystemSection = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const { scrollProgress } = useScrollAnimation(sectionRef);

  const kairoFeatures = [
    "Free value first.",
    "High intent leads only.",
    "Pre-qualifies your pipeline.",
  ];

  const airoFeatures = [
    "Fully customized CRM logic",
    "Competitor analysis",
    "Objection profiles and deal notes auto-generated",
  ];

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-white overflow-hidden"
      aria-label="Featured systems"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
          <SystemCard
            title="KAIRO"
            subtitle="Your Automated Growth Audit System"
            description="Turn cold leads into warm calls using a self-diagnosing growth audit — powered by AI."
            features={kairoFeatures}
            buttonText="Run Your Free Audit"
            alignment="left"
            animationDelay={0}
            scrollProgress={scrollProgress}
          />

          <SystemCard
            title="AIRO"
            subtitle="Your AI Sales Prep System"
            description="Help your sales team close faster by letting AI prep every call, every time."
            features={airoFeatures}
            buttonText="See How Airo Works"
            alignment="right"
            animationDelay={3}
            scrollProgress={scrollProgress}
          />
        </div>
      </div>
    </section>
  );
};

// How It Works Section Component
const HowItWorksSection = () => {
  const steps = [
    {
      title: "Diagnose",
      description: "We identify the best-fit system for your business.",
      alignment: "left",
    },
    {
      title: "Install",
      description: "Our team builds and customizes it in under 2 weeks.",
      alignment: "center",
    },
    {
      title: "Run",
      description:
        "The system operates automatically. You monitor the dashboard. We handle the rest.",
      alignment: "right",
    },
  ];

  return (
    <section className="py-20 bg-gray-100" aria-label="How it works">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-7xl text-right font-medium text-gray-800 uppercase leading-tight mb-8">
            Built to Be Installed. Not Managed.
          </h2>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 lg:gap-12 mb-12">
          {steps.map((step, index) => (
            <div
              key={step.title}
              className={`space-y-4 ${
                step.alignment === "center"
                  ? "text-center"
                  : step.alignment === "right"
                  ? "text-right"
                  : ""
              }`}
            >
              <div className="bg-white p-8 space-y-4">
                <h3 className="text-xl font-bold text-gray-900">
                  {step.title}
                </h3>
                <p className="text-base text-gray-600">{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <button
            type="button"
            className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2"
          >
            Book Your System Assessment
          </button>
        </div>
      </div>
    </section>
  );
};

// Why Ascenda Differentiator Section Component
const WhyAscendaSection = () => {
  const differentiators = [
    "Productized AI Systems",
    "Built in days, not months",
    "Designed to scale automatically",
    "Boutique, high-end experience",
  ];

  return (
    <section
      className="py-20 bg-white border-t border-gray-200"
      aria-label="Why choose Ascenda"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
            Ascenda vs. The Rest
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {differentiators.map((item, index) => (
            <div key={`diff-${index}`} className="text-center space-y-3">
              <div className="text-2xl text-green-600" aria-hidden="true">
                ✔
              </div>
              <h3 className="font-semibold text-gray-900">{item}</h3>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Testimonials Section Component
const TestimonialsSection = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const { scrollProgress } = useScrollAnimation(sectionRef);

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gray-100 overflow-hidden"
      aria-label="Customer testimonials"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12">
          {TESTIMONIALS.map((testimonial, index) => {
            const cardStyles = useAnimationStyles(
              scrollProgress,
              index === 0 ? 150 : -150,
              index * 300
            );

            return (
              <blockquote
                key={testimonial.id}
                className="bg-white p-8 lg:p-10 space-y-6 transform transition-all duration-700 ease-out"
                style={cardStyles}
              >
                <p className="text-lg text-gray-700 italic leading-relaxed">
                  "{testimonial.quote}"
                </p>
                <footer className="border-t pt-4">
                  <cite className="not-italic">
                    <p className="font-semibold text-gray-900">
                      {testimonial.name}
                    </p>
                    <p className="text-sm text-gray-600">{testimonial.title}</p>
                  </cite>
                </footer>
              </blockquote>
            );
          })}
        </div>
      </div>
    </section>
  );
};

// Final CTA Section Component
const FinalCTASection = () => {
  return (
    <section
      className="min-h-screen bg-gray-900 flex items-center"
      aria-label="Final call to action"
    >
      <div className="container mx-auto px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto space-y-8">
          <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
            Let's Turn Your Business Into a Machine
          </h2>

          <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
            If you're tired of manual hustle, inconsistent leads, and bloated
            agencies… It's time to install real growth infrastructure.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
            <button
              type="button"
              className="bg-white cursor-pointer hover:bg-gray-100 text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white hover:border-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              Run Your Free Growth Audit
            </button>
            <button
              type="button"
              className="bg-transparent cursor-pointer hover:bg-white text-white hover:text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              Book a Discovery Call
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default function Home() {
  return (
    <main role="main">
      <HeroSection />
      <PlugAndPlaySection />
      <CredibilitySection />
      <WhatWeDoSection />
      <FeaturedSystemSection />
      <HowItWorksSection />
      <WhyAscendaSection />
      <TestimonialsSection />
      <FinalCTASection />
    </main>
  );
}

@import "tailwindcss";

@theme inline {
  --font-sans: var(--font-dm-sans);
}

/* Parallax floating animations */
@keyframes parallaxFloat1 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes parallaxFloat2 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes parallaxFloat3 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-12px); }
}


/* button:hover {
  color: black;
}

button:after {
  content: "";
  background: white;
  position: absolute;
  z-index: -1;
  left: -10%;
  right: -10%;
  top: 0;
  bottom: 0;
  transform: skewX(-45deg) scale(0, 1);
  transition: all 0.5s;
}

button:hover:after {
  transform: skewX(-45deg) scale(1, 1);
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
} */